<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="text-light-text"><?= esc($org['name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Edit Admin</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="mb-1 fw-bold text-light-text">
                <i class="fas fa-user-edit me-2 text-primary"></i>Edit System Administrator
            </h3>
            <p class="text-secondary mb-0">Update administrator account for <?= esc($org['name']) ?></p>
        </div>
        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-2"></i> Back to Organization
        </a>
    </div>

    <!-- Admin Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <h5 class="fw-bold mb-0 text-white">
                        <i class="fas fa-user-edit me-2"></i>Edit System Administrator
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-primary bg-opacity-25 mb-3">
                            <i class="fas fa-user-shield text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="text-light-text">Update Administrator Details</h5>
                        <p class="text-secondary">Modify the administrator account information</p>
                    </div>

                    <?= form_open('dakoii/organization/admin/update') ?>
                    
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_name" class="form-label text-light-text fw-bold">Full Name</label>
                                <input type="text" class="form-control" name="name" id="admin_name" 
                                       value="<?= esc($admin['name']) ?>" required 
                                       placeholder="Enter administrator's full name">
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_username" class="form-label text-light-text fw-bold">Username</label>
                                <input type="text" class="form-control" name="username" id="admin_username" 
                                       value="<?= esc($admin['username']) ?>" required 
                                       placeholder="Enter unique username">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_password" class="form-label text-light-text fw-bold">Password</label>
                                <input type="password" class="form-control" name="password" id="admin_password" 
                                       placeholder="Leave blank to keep current password">
                                <div class="form-text text-secondary">Only fill this if you want to change the password</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_role" class="form-label text-light-text fw-bold">Role</label>
                                <select class="form-select" name="role" id="admin_role" required>
                                    <option value="">Select Role</option>
                                    <option value="admin" <?= $admin['role'] == 'admin' ? 'selected' : '' ?>>Administrator</option>
                                    <option value="user" <?= $admin['role'] == 'user' ? 'selected' : '' ?>>Standard User</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="admin_active" name="is_active" value="1" 
                                   <?= (isset($admin['status']) && $admin['status'] == 1) ? 'checked' : '' ?>>
                            <label class="form-check-label text-light-text fw-bold" for="admin_active">Active Account</label>
                            <div class="form-text text-secondary">Uncheck to disable this administrator account</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <input type="hidden" name="id" value="<?= $admin['id'] ?>">
                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">
                        
                        <button type="submit" class="btn btn-primary text-white btn-lg">
                            <i class="fas fa-save me-2"></i> Update Administrator
                        </button>
                        
                        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
                            <i class="fas fa-times me-2"></i> Cancel
                        </a>
                    </div>
                    
                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
